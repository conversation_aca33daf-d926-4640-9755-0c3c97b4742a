import React, { useState, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Target, Save } from "lucide-react";
import { useCreateTask, useUpdateTask } from "@/hooks/queries/task";
import { toast } from "sonner";

// Helper function to format date-time for input
const formatDateTimeLocal = (dateString: string): string => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

interface MeetingTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  task?: {
    id: string;
    title: string;
    description: string;
    priority: string;
    "start-date": string;
    "end-date": string;
    "meeting-url": string | null;
    note: string;
    status: string;
    "milestone-id": string;
  } | null;
  milestoneId: string;
}

interface Task {
  id: string;
  title: string;
  description: string;
  priority: string;
  "start-date": string;
  "end-date": string;
  "meeting-url": string | null;
  note: string;
  status: string;
  "milestone-id": string;
}

const MeetingTaskModal: React.FC<MeetingTaskModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  task,
  milestoneId,
}) => {
  // Reset forms when modal closes
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleTaskFormSave = useCallback(() => {
    handleClose();
    // Call onSuccess callback to refresh parent data
    if (onSuccess) {
      onSuccess();
    }
  }, [handleClose, onSuccess]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center gap-3">
            <Target className="h-6 w-6 text-blue-600" />
            <div>
              <DialogTitle className="text-xl font-bold">
                {task ? "Update Task" : "Create New Task"}
              </DialogTitle>
              <p className="text-sm text-gray-600 mt-1">
                {task ? "Edit task details" : "Create a new meeting task"}
              </p>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden min-h-0">
          <TaskForm
            task={task ?? undefined}
            milestoneId={milestoneId}
            onSave={handleTaskFormSave}
            onCancel={handleClose}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Task Form Component
const TaskForm: React.FC<{
  task?: Task;
  milestoneId: string;
  onSave: () => void;
  onCancel: () => void;
}> = ({ task, milestoneId, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: task?.title || "",
    description: task?.description || "",
    // Mặc định Priority là "High" cho Create Modal, giữ nguyên priority cũ khi Update
    priority: (task?.priority as "Low" | "Medium" | "High") || "High",
    "start-date": task?.["start-date"]
      ? formatDateTimeLocal(task["start-date"])
      : "",
    "end-date": task?.["end-date"] ? formatDateTimeLocal(task["end-date"]) : "",
    note: task?.note || "",
    "meeting-url": task?.["meeting-url"] || "",
  });

  const createTaskMutation = useCreateTask();
  const updateTaskMutation = useUpdateTask();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const submitData = {
        ...formData,
        "milestone-id": milestoneId,
        "start-date": formData["start-date"]
          ? new Date(formData["start-date"]).toISOString()
          : "",
        "end-date": formData["end-date"]
          ? new Date(formData["end-date"]).toISOString()
          : "",
        progress: 0,
        overdue: 0,
      };

      if (task) {
        await updateTaskMutation.mutateAsync({
          taskId: task.id,
          taskData: submitData,
        });
        toast.success("Task updated successfully");
      } else {
        await createTaskMutation.mutateAsync(submitData);
        toast.success("Task created successfully");
      }
      onSave();
    } catch (error) {
      console.error("Error saving task:", error);
      toast.error(`Failed to ${task ? "update" : "create"} task`);
    }
  };

  const handleFormDataChange = useCallback((field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  }, []);

  return (
    <div className="max-h-[60vh] overflow-y-auto">
      <form onSubmit={handleSubmit} className="space-y-4 pr-2">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Task Name
          </label>
          <Input
            type="text"
            value={formData.name}
            onChange={(e) => handleFormDataChange("name", e.target.value)}
            required
            placeholder="Enter task name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <Textarea
            value={formData.description}
            onChange={(e) =>
              handleFormDataChange("description", e.target.value)
            }
            placeholder="Enter task description"
            rows={3}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Priority
          </label>
          <Select
            value={formData.priority}
            onValueChange={(value: "Low" | "Medium" | "High") =>
              handleFormDataChange("priority", value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Low">Low</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="High">High</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date & Time
            </label>
            <Input
              type="datetime-local"
              value={formData["start-date"]}
              onChange={(e) =>
                handleFormDataChange("start-date", e.target.value)
              }
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date & Time
            </label>
            <Input
              type="datetime-local"
              value={formData["end-date"]}
              onChange={(e) => handleFormDataChange("end-date", e.target.value)}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Meeting URL
          </label>
          <Input
            type="url"
            value={formData["meeting-url"]}
            onChange={(e) =>
              handleFormDataChange("meeting-url", e.target.value)
            }
            placeholder="https://meet.google.com/... or https://zoom.us/j/..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Notes
          </label>
          <Textarea
            value={formData.note}
            onChange={(e) => handleFormDataChange("note", e.target.value)}
            placeholder="Additional notes"
            rows={2}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="w-4 h-4 mr-2" />
            {task ? "Update" : "Create"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default MeetingTaskModal;
